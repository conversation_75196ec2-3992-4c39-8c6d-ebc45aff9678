package com.calculator.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun CalculatorApp() {
    var display by remember { mutableStateOf("0") }
    var previousValue by remember { mutableStateOf(0.0) }
    var operation by remember { mutableStateOf("") }
    var waitingForOperand by remember { mutableStateOf(false) }

    val gradientBackground = Brush.verticalGradient(
        colors = listOf(
            Color(0xFF667eea),
            Color(0xFF764ba2)
        )
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(gradientBackground)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Display
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White.copy(alpha = 0.9f)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.CenterEnd
            ) {
                Text(
                    text = display,
                    fontSize = 36.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D3748),
                    textAlign = TextAlign.End,
                    modifier = Modifier.padding(horizontal = 20.dp)
                )
            }
        }

        // Buttons Grid
        val buttons = listOf(
            listOf("C", "±", "%", "÷"),
            listOf("7", "8", "9", "×"),
            listOf("4", "5", "6", "-"),
            listOf("1", "2", "3", "+"),
            listOf("0", ".", "=")
        )

        buttons.forEach { row ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                row.forEach { buttonText ->
                    val weight = if (buttonText == "0") 2f else 1f
                    CalculatorButton(
                        text = buttonText,
                        modifier = Modifier.weight(weight),
                        onClick = {
                            handleButtonClick(
                                buttonText,
                                display,
                                previousValue,
                                operation,
                                waitingForOperand
                            ) { newDisplay, newPrevious, newOperation, newWaiting ->
                                display = newDisplay
                                previousValue = newPrevious
                                operation = newOperation
                                waitingForOperand = newWaiting
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun CalculatorButton(
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    val buttonColor = when {
        text in listOf("C", "±", "%") -> Color(0xFFA0AEC0)
        text in listOf("÷", "×", "-", "+", "=") -> Color(0xFFFF6B6B)
        else -> Color.White
    }
    
    val textColor = when {
        text in listOf("÷", "×", "-", "+", "=") -> Color.White
        else -> Color(0xFF2D3748)
    }

    Button(
        onClick = onClick,
        modifier = modifier
            .height(70.dp)
            .clip(RoundedCornerShape(20.dp)),
        colors = ButtonDefaults.buttonColors(
            containerColor = buttonColor
        ),
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = 6.dp,
            pressedElevation = 2.dp
        )
    ) {
        Text(
            text = text,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = textColor
        )
    }
}

fun handleButtonClick(
    buttonText: String,
    currentDisplay: String,
    previousValue: Double,
    operation: String,
    waitingForOperand: Boolean,
    onUpdate: (String, Double, String, Boolean) -> Unit
) {
    when (buttonText) {
        "C" -> {
            onUpdate("0", 0.0, "", false)
        }
        "±" -> {
            val value = currentDisplay.toDoubleOrNull() ?: 0.0
            onUpdate((-value).toString(), previousValue, operation, waitingForOperand)
        }
        "%" -> {
            val value = currentDisplay.toDoubleOrNull() ?: 0.0
            onUpdate((value / 100).toString(), previousValue, operation, waitingForOperand)
        }
        in listOf("÷", "×", "-", "+") -> {
            val inputValue = currentDisplay.toDoubleOrNull() ?: 0.0
            
            if (operation.isNotEmpty() && !waitingForOperand) {
                val result = calculate(previousValue, inputValue, operation)
                onUpdate(result.toString(), result, buttonText, true)
            } else {
                onUpdate(currentDisplay, inputValue, buttonText, true)
            }
        }
        "=" -> {
            val inputValue = currentDisplay.toDoubleOrNull() ?: 0.0
            if (operation.isNotEmpty()) {
                val result = calculate(previousValue, inputValue, operation)
                onUpdate(result.toString(), 0.0, "", false)
            }
        }
        "." -> {
            if (!currentDisplay.contains(".")) {
                val newDisplay = if (waitingForOperand) "0." else "$currentDisplay."
                onUpdate(newDisplay, previousValue, operation, false)
            }
        }
        else -> {
            val newDisplay = if (waitingForOperand || currentDisplay == "0") {
                buttonText
            } else {
                currentDisplay + buttonText
            }
            onUpdate(newDisplay, previousValue, operation, false)
        }
    }
}

fun calculate(firstValue: Double, secondValue: Double, operation: String): Double {
    return when (operation) {
        "+" -> firstValue + secondValue
        "-" -> firstValue - secondValue
        "×" -> firstValue * secondValue
        "÷" -> if (secondValue != 0.0) firstValue / secondValue else 0.0
        else -> secondValue
    }
}
