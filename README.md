# تطبيق الحاسبة الجميلة 🧮

تطبيق حاسبة Android مطور بـ Kotlin باستخدام Jetpack Compose مع تصميم جميل وألوان جذابة.

## المميزات ✨

- **تصميم جميل**: واجهة مستخدم عصرية مع تدرجات لونية جذابة
- **ألوان متناسقة**: نظام ألوان مدروس يوفر تجربة بصرية ممتعة
- **أزرار تفاعلية**: أزرار مع تأثيرات الظل والانيميشن
- **عمليات حسابية كاملة**: جمع، طرح، ضرب، قسمة، نسبة مئوية
- **واجهة سهلة الاستخدام**: تصميم بديهي وسهل الاستخدام

## التقنيات المستخدمة 🛠️

- **Kotlin**: لغة البرمجة الأساسية
- **Jetpack Compose**: لبناء واجهة المستخدم
- **Material Design 3**: لنظام التصميم
- **Android Studio**: بيئة التطوير

## الألوان المستخدمة 🎨

- **خلفية متدرجة**: من الأزرق البنفسجي إلى البنفسجي
- **أزرار الأرقام**: أبيض مع نص رمادي داكن
- **أزرار العمليات**: أحمر مرجاني مع نص أبيض
- **أزرار الوظائف**: رمادي فاتح مع نص داكن

## كيفية التشغيل 🚀

1. افتح المشروع في Android Studio
2. تأكد من تثبيت Android SDK
3. قم بتشغيل التطبيق على جهاز أو محاكي Android

## هيكل المشروع 📁

```
app/
├── src/main/java/com/calculator/app/
│   ├── MainActivity.kt          # النشاط الرئيسي
│   ├── CalculatorApp.kt         # واجهة الحاسبة الرئيسية
│   └── ui/theme/               # ملفات الثيم والألوان
│       ├── Color.kt
│       ├── Theme.kt
│       └── Type.kt
└── src/main/res/               # الموارد
    ├── values/
    └── xml/
```

## الوظائف المتاحة 🔢

- **الأرقام**: 0-9
- **العمليات الأساسية**: +، -، ×، ÷
- **وظائف إضافية**:
  - C: مسح الشاشة
  - ±: تغيير الإشارة
  - %: النسبة المئوية
  - .: الفاصلة العشرية
  - =: حساب النتيجة

تم تطوير هذا التطبيق بعناية لتوفير تجربة مستخدم ممتازة مع تصميم جميل وأداء سلس! 🎉
