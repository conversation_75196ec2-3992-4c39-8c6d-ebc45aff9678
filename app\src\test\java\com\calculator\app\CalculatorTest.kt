package com.calculator.app

import org.junit.Test
import org.junit.Assert.*

class CalculatorTest {

    @Test
    fun addition_isCorrect() {
        val result = calculate(2.0, 3.0, "+")
        assertEquals(5.0, result, 0.001)
    }

    @Test
    fun subtraction_isCorrect() {
        val result = calculate(5.0, 3.0, "-")
        assertEquals(2.0, result, 0.001)
    }

    @Test
    fun multiplication_isCorrect() {
        val result = calculate(4.0, 3.0, "×")
        assertEquals(12.0, result, 0.001)
    }

    @Test
    fun division_isCorrect() {
        val result = calculate(10.0, 2.0, "÷")
        assertEquals(5.0, result, 0.001)
    }

    @Test
    fun division_byZero_returnsZero() {
        val result = calculate(10.0, 0.0, "÷")
        assertEquals(0.0, result, 0.001)
    }
}
